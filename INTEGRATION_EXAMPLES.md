# Enhanced APIs Integration Examples

This document provides practical examples of how to integrate the four new enhanced APIs into your existing provider portal chat module.

## Quick Start

### 1. Import the Enhanced APIs

```typescript
// Import individual functions
import { sendOtpWithValidation, getRoomMembers } from '@/lib/enhancedApi';

// Import hooks
import { useEnhancedAuth, useRoomManagement } from '@/hooks';

// Import components
import { RoomMembersWidget, CreateRoomWidget } from '@/components/enhanced-auth';

// Or import everything from the enhanced module
import { 
  useEnhancedAuth, 
  useRoomManagement, 
  RoomMembersWidget 
} from '@/lib/enhanced';
```

### 2. Add Room Members to Existing Chat

```tsx
// In your existing chat component
import { RoomMembersWidget } from '@/components/enhanced-auth/RoomMembersWidget';

function ExistingChatComponent({ roomId }: { roomId: string }) {
  return (
    <div className="chat-container">
      {/* Your existing chat UI */}
      <div className="chat-messages">
        {/* Existing message components */}
      </div>
      
      {/* Add room members widget */}
      <div className="chat-sidebar">
        <RoomMembersWidget 
          roomId={roomId}
          collapsible={true}
          showRefreshButton={true}
        />
      </div>
    </div>
  );
}
```

### 3. Enhanced Authentication in Login Flow

```tsx
// Enhance your existing login component
import { useAuth } from '@/context/auth';

function LoginComponent() {
  const { 
    // Existing methods
    login, 
    verifyOtp,
    // New enhanced methods
    loginWithRoomValidation,
    verifyOtpEnhanced 
  } = useAuth();

  const [useEnhancedAuth, setUseEnhancedAuth] = useState(false);
  const [roomId, setRoomId] = useState('');

  const handleLogin = async (email: string, role: UserRole) => {
    if (useEnhancedAuth && roomId) {
      // Use enhanced authentication with room validation
      return await loginWithRoomValidation?.(roomId, email, role as ExtendedUserRole);
    } else {
      // Use existing authentication
      return await login(email, role);
    }
  };

  return (
    <form>
      {/* Existing form fields */}
      
      {/* Optional: Add room-based authentication */}
      <div className="enhanced-auth-option">
        <label>
          <input 
            type="checkbox" 
            checked={useEnhancedAuth}
            onChange={(e) => setUseEnhancedAuth(e.target.checked)}
          />
          Use room-based authentication
        </label>
        
        {useEnhancedAuth && (
          <input
            type="text"
            placeholder="Room ID"
            value={roomId}
            onChange={(e) => setRoomId(e.target.value)}
          />
        )}
      </div>
    </form>
  );
}
```

## Specific Use Cases

### Use Case 1: Provider Dashboard with Room Creation

```tsx
import { CreateRoomWidget } from '@/components/enhanced-auth/CreateRoomWidget';

function ProviderDashboard() {
  const handleRoomCreated = (roomData: any) => {
    // Redirect to the new room
    window.location.href = roomData.room_url;
  };

  return (
    <div className="dashboard">
      <h1>Provider Dashboard</h1>
      
      {/* Existing dashboard content */}
      
      <div className="quick-actions">
        <CreateRoomWidget 
          defaultRole="BUSER"
          autoRedirect={true}
          onRoomCreated={handleRoomCreated}
        />
      </div>
    </div>
  );
}
```

### Use Case 2: Patient Portal with Order-based Room Access

```tsx
import { useRoomManagement } from '@/hooks/useRoomManagement';

function PatientOrderDetails({ orderGuid }: { orderGuid: string }) {
  const { createRoomWithOrderValidation, isLoading } = useRoomManagement();
  
  const joinChatRoom = async () => {
    const result = await createRoomWithOrderValidation(orderGuid, 'USER');
    if (result.success) {
      // Navigate to chat room
      window.location.href = `/chat/${result.roomData?.room.room_identifier}`;
    }
  };

  return (
    <div className="order-details">
      {/* Existing order information */}
      
      <div className="chat-access">
        <button 
          onClick={joinChatRoom}
          disabled={isLoading}
          className="btn-primary"
        >
          {isLoading ? 'Joining...' : 'Join Chat Room'}
        </button>
      </div>
    </div>
  );
}
```

### Use Case 3: Admin Panel with Room Management

```tsx
import { useRoomManagement } from '@/hooks/useRoomManagement';
import { RoomMembersWidget } from '@/components/enhanced-auth/RoomMembersWidget';

function AdminRoomManagement() {
  const [selectedRoomId, setSelectedRoomId] = useState('');
  
  return (
    <div className="admin-panel">
      <div className="room-selector">
        <input
          type="text"
          placeholder="Enter Room ID"
          value={selectedRoomId}
          onChange={(e) => setSelectedRoomId(e.target.value)}
        />
      </div>
      
      {selectedRoomId && (
        <RoomMembersWidget 
          roomId={selectedRoomId}
          autoLoad={true}
          showRefreshButton={true}
        />
      )}
    </div>
  );
}
```

### Use Case 4: Custom Authentication Flow

```tsx
import { useEnhancedAuth } from '@/hooks/useEnhancedAuth';

function CustomAuthFlow({ roomId }: { roomId: string }) {
  const {
    sendOtpWithRoomValidation,
    verifyOtpAndAuthenticate,
    isLoading,
    error,
    pendingAuth
  } = useEnhancedAuth();

  const [step, setStep] = useState<'email' | 'otp' | 'complete'>('email');
  const [email, setEmail] = useState('');
  const [otp, setOtp] = useState('');

  const handleSendOtp = async () => {
    const success = await sendOtpWithRoomValidation(roomId, email, 'USER');
    if (success) {
      setStep('otp');
    }
  };

  const handleVerifyOtp = async () => {
    const result = await verifyOtpAndAuthenticate(otp);
    if (result.success) {
      setStep('complete');
      // Handle successful authentication
    }
  };

  return (
    <div className="custom-auth">
      {step === 'email' && (
        <div>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Enter your email"
          />
          <button onClick={handleSendOtp} disabled={isLoading}>
            Send OTP
          </button>
        </div>
      )}

      {step === 'otp' && (
        <div>
          <input
            type="text"
            value={otp}
            onChange={(e) => setOtp(e.target.value)}
            placeholder="Enter OTP"
          />
          <button onClick={handleVerifyOtp} disabled={isLoading}>
            Verify OTP
          </button>
        </div>
      )}

      {step === 'complete' && (
        <div>Authentication successful!</div>
      )}

      {error && <div className="error">{error}</div>}
    </div>
  );
}
```

## API Response Handling

### Handling API Responses

```typescript
import { getRoomMembers } from '@/lib/enhancedApi';

async function handleGetRoomMembers(roomId: string) {
  const response = await getRoomMembers(roomId);
  
  switch (response.statusCode) {
    case 200:
      // Success
      console.log('Members:', response.data?.members);
      break;
    case 401:
      // Unauthorized - redirect to login
      window.location.href = '/login';
      break;
    case 403:
      // Forbidden - show access denied message
      alert('You do not have permission to view this room');
      break;
    case 404:
      // Not found
      alert('Room not found');
      break;
    default:
      // Other errors
      console.error('Error:', response.message);
  }
}
```

## Testing the Implementation

### 1. Test the Demo Page

Visit `/enhanced-demo` to see all APIs in action with a complete UI.

### 2. Test Individual Components

```tsx
// Test room members widget
<RoomMembersWidget roomId="test_room_123" />

// Test room creation widget  
<CreateRoomWidget defaultRole="USER" />
```

### 3. Test API Functions Directly

```typescript
// Test in browser console or component
import { sendOtpWithValidation } from '@/lib/enhancedApi';

sendOtpWithValidation({
  room_identifier: "test_room",
  email: "<EMAIL>", 
  role: "USER"
}).then(console.log);
```

## Migration Strategy

1. **Phase 1**: Add new APIs alongside existing ones (no breaking changes)
2. **Phase 2**: Gradually replace existing authentication flows with enhanced versions
3. **Phase 3**: Add room management features to existing chat components
4. **Phase 4**: Implement order-based room creation in relevant workflows

The enhanced APIs are designed to work alongside your existing code without requiring immediate migration.
