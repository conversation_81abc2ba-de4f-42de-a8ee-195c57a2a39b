# Enhanced Chat APIs - Client Implementation

This document describes the client-side implementation of the four new enhanced APIs for the provider portal chat module.

## Overview

The enhanced APIs provide improved security, validation, and functionality for:
1. **Room-based OTP authentication** with membership validation
2. **Enhanced OTP verification** with improved token handling
3. **Token-protected room member retrieval**
4. **Order-validated room creation** with relationship verification

## Files Added/Modified

### New Files Created

1. **`src/lib/enhancedApi.ts`** - Core API client functions
2. **`src/hooks/useEnhancedAuth.ts`** - Authentication hook
3. **`src/hooks/useRoomManagement.ts`** - Room management hook
4. **`src/components/enhanced-auth/EnhancedAuthDemo.tsx`** - Complete demo component
5. **`src/components/enhanced-auth/RoomMembersWidget.tsx`** - Reusable room members widget
6. **`src/components/enhanced-auth/CreateRoomWidget.tsx`** - Reusable room creation widget

### Modified Files

1. **`src/types/chat.ts`** - Added new types and API endpoints
2. **`src/context/auth.tsx`** - Added enhanced authentication methods

## API Implementation

### 1. Send OTP with Room Validation

**Endpoint:** `POST /auth/send-otp-with-validation`

```typescript
import { sendOtpWithValidation } from '@/lib/enhancedApi';

const result = await sendOtpWithValidation({
  room_identifier: "room_123",
  email: "<EMAIL>",
  role: "USER"
});
```

**Using the Hook:**
```typescript
import { useEnhancedAuth } from '@/hooks/useEnhancedAuth';

const { sendOtpWithRoomValidation } = useEnhancedAuth();
const success = await sendOtpWithRoomValidation("room_123", "<EMAIL>", "USER");
```

### 2. Enhanced OTP Verification

**Endpoint:** `POST /auth/verify-otp`

```typescript
import { verifyOtpEnhanced } from '@/lib/enhancedApi';

const result = await verifyOtpEnhanced({
  email: "<EMAIL>",
  otp: "123456"
});
```

**Using the Hook:**
```typescript
const { verifyOtpAndAuthenticate } = useEnhancedAuth();
const result = await verifyOtpAndAuthenticate("123456");
if (result.success) {
  console.log('User:', result.user);
  console.log('Token:', result.token);
}
```

### 3. Get Room Members (Token Protected)

**Endpoint:** `GET /room/{roomId}/members`

```typescript
import { getRoomMembers } from '@/lib/enhancedApi';

const result = await getRoomMembers("room_123");
if (result.statusCode === 200) {
  console.log('Members:', result.data.members);
}
```

**Using the Hook:**
```typescript
import { useRoomManagement } from '@/hooks/useRoomManagement';

const { fetchRoomMembers, roomMembers, totalMembers } = useRoomManagement();
const success = await fetchRoomMembers("room_123");
```

**Using the Widget:**
```tsx
import { RoomMembersWidget } from '@/components/enhanced-auth/RoomMembersWidget';

<RoomMembersWidget 
  roomId="room_123" 
  autoLoad={true}
  showRefreshButton={true}
  collapsible={true}
/>
```

### 4. Create Room with Order Validation

**Endpoint:** `POST /room/create/with-order`

```typescript
import { createRoomWithOrder } from '@/lib/enhancedApi';

const result = await createRoomWithOrder({
  order_guid: "550e8400-e29b-41d4-a716-************",
  role: "BUSER"
});
```

**Using the Hook:**
```typescript
const { createRoomWithOrderValidation } = useRoomManagement();
const result = await createRoomWithOrderValidation(
  "550e8400-e29b-41d4-a716-************", 
  "BUSER"
);
```

**Using the Widget:**
```tsx
import { CreateRoomWidget } from '@/components/enhanced-auth/CreateRoomWidget';

<CreateRoomWidget 
  defaultRole="USER"
  autoRedirect={true}
  onRoomCreated={(roomData) => console.log('Room created:', roomData)}
/>
```

## Integration Examples

### Basic Authentication Flow

```tsx
import { useEnhancedAuth } from '@/hooks/useEnhancedAuth';

function EnhancedLogin() {
  const { 
    sendOtpWithRoomValidation, 
    verifyOtpAndAuthenticate, 
    isLoading, 
    error 
  } = useEnhancedAuth();

  const handleLogin = async () => {
    const success = await sendOtpWithRoomValidation(
      "room_123", 
      "<EMAIL>", 
      "USER"
    );
    if (success) {
      // Show OTP input form
    }
  };

  const handleVerifyOtp = async (otp: string) => {
    const result = await verifyOtpAndAuthenticate(otp);
    if (result.success) {
      // Redirect to chat or update UI
    }
  };

  return (
    // Your login form JSX
  );
}
```

### Room Management Integration

```tsx
import { useRoomManagement } from '@/hooks/useRoomManagement';
import { RoomMembersWidget } from '@/components/enhanced-auth/RoomMembersWidget';

function ChatRoom({ roomId }: { roomId: string }) {
  return (
    <div className="chat-container">
      {/* Existing chat components */}
      
      {/* Add room members widget */}
      <RoomMembersWidget 
        roomId={roomId}
        className="mt-4"
        collapsible={true}
      />
    </div>
  );
}
```

### Using Enhanced Auth Context

```tsx
import { useAuth } from '@/context/auth';

function MyComponent() {
  const { 
    loginWithRoomValidation, 
    verifyOtpEnhanced,
    isAuthenticated 
  } = useAuth();

  // Enhanced authentication methods are now available
  // alongside existing auth methods
}
```

## Error Handling

All APIs return consistent error responses:

```typescript
interface ApiResponse<T> {
  statusCode: number;
  message: string;
  data?: T;
  error?: string;
}
```

Common error codes:
- `400`: Bad request (validation errors)
- `401`: Unauthorized (invalid/missing token)
- `403`: Forbidden (insufficient permissions)
- `404`: Not found (room/order/user not found)
- `500`: Server error

## Security Features

1. **JWT Token Authentication** - All protected endpoints require valid tokens
2. **Role-based Access Control** - Users can only access rooms they're members of
3. **Order Relationship Validation** - Room creation validates provider/patient relationships
4. **Input Validation** - All inputs are validated on both client and server
5. **Secure Token Storage** - Tokens stored in secure HTTP-only cookies

## Demo Component

A complete demo component is available at `src/components/enhanced-auth/EnhancedAuthDemo.tsx` that showcases all four APIs with a full UI.

To use the demo:

```tsx
import { EnhancedAuthDemo } from '@/components/enhanced-auth/EnhancedAuthDemo';

function DemoPage() {
  return <EnhancedAuthDemo />;
}
```

## Testing

The widgets include sample data generators and test utilities:

- **CreateRoomWidget** includes a "Use Sample UUID" button for testing
- **RoomMembersWidget** includes error handling and retry mechanisms
- All hooks include comprehensive error handling and loading states

## Migration Notes

- The enhanced APIs work alongside existing authentication flows
- Existing components can gradually adopt the new APIs
- No breaking changes to existing functionality
- Enhanced methods are optional in the AuthContext interface
