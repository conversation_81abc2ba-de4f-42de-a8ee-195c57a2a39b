"use client";
import { useState, useCallback } from 'react';
import { toast } from 'sonner';
import { tokenManager } from '@/lib/tokenManager';
import { 
  sendOtpWithValidation,
  verifyOtpEnhanced,
  isValidEmail
} from '@/lib/enhancedApi';
import { 
  SendOtpWithValidationRequest,
  VerifyOtpEnhancedRequest,
  ExtendedUserRole,
  User
} from '@/types/chat';

interface UseEnhancedAuthReturn {
  isLoading: boolean;
  error: string | null;
  pendingAuth: SendOtpWithValidationRequest | null;
  sendOtpWithRoomValidation: (
    roomIdentifier: string,
    email: string,
    role: ExtendedUserRole
  ) => Promise<boolean>;
  verifyOtpAndAuthenticate: (otp: string) => Promise<{ success: boolean; user?: User; token?: string }>;
  clearError: () => void;
  clearPendingAuth: () => void;
}

/**
 * Enhanced authentication hook for room-based OTP authentication
 * Supports the new enhanced APIs with room validation and improved security
 */
export const useEnhancedAuth = (): UseEnhancedAuthReturn => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pendingAuth, setPendingAuth] = useState<SendOtpWithValidationRequest | null>(null);

  /**
   * Step 1: Send OTP with room validation
   * Validates that the user is part of the specified room before sending OTP
   */
  const sendOtpWithRoomValidation = useCallback(async (
    roomIdentifier: string,
    email: string,
    role: ExtendedUserRole
  ): Promise<boolean> => {
    // Input validation
    if (!roomIdentifier.trim()) {
      setError('Room identifier is required');
      toast.error('Room identifier is required');
      return false;
    }

    if (!email.trim() || !isValidEmail(email)) {
      setError('Valid email address is required');
      toast.error('Valid email address is required');
      return false;
    }

    if (!role) {
      setError('User role is required');
      toast.error('User role is required');
      return false;
    }

    setIsLoading(true);
    setError(null);

    try {
      const request: SendOtpWithValidationRequest = {
        room_identifier: roomIdentifier.trim(),
        email: email.trim().toLowerCase(),
        role,
      };

      const response = await sendOtpWithValidation(request);

      if (response.statusCode === 200) {
        setPendingAuth(request);
        toast.success('OTP sent to your email');
        return true;
      } else {
        const errorMessage = response.message || 'Failed to send OTP';
        setError(errorMessage);
        toast.error(errorMessage);
        return false;
      }
    } catch (err: any) {
      const errorMessage = err?.message || 'Network error occurred';
      setError(errorMessage);
      toast.error(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Step 2: Verify OTP and complete authentication
   * Uses the enhanced OTP verification with improved token handling
   */
  const verifyOtpAndAuthenticate = useCallback(async (
    otp: string
  ): Promise<{ success: boolean; user?: User; token?: string }> => {
    if (!pendingAuth) {
      setError('No pending authentication found. Please request OTP first.');
      toast.error('No pending authentication found');
      return { success: false };
    }

    if (!otp.trim() || otp.trim().length < 4) {
      setError('Valid OTP is required (4-6 characters)');
      toast.error('Valid OTP is required');
      return { success: false };
    }

    setIsLoading(true);
    setError(null);

    try {
      const request: VerifyOtpEnhancedRequest = {
        email: pendingAuth.email,
        otp: otp.trim(),
      };

      const response = await verifyOtpEnhanced(request);

      if (response.statusCode === 200 && response.data) {
        const { user: authUser, token: authToken } = response.data;
        
        // Convert the response user to our User interface
        const user: User = {
          user_id: authUser.user_id.toString(),
          user_guid: authUser.user_guid,
          first_name: authUser.first_name,
          last_name: authUser.last_name,
          email: authUser.email,
          role: pendingAuth.role as any, // Use the role from the original request
          status: authUser.status,
        };

        // Store authentication data
        await tokenManager.setToken(authToken, user);
        setPendingAuth(null);
        
        toast.success('Authentication successful!');
        return { success: true, user, token: authToken };
      } else {
        const errorMessage = response.message || 'Failed to verify OTP';
        setError(errorMessage);
        toast.error(errorMessage);
        return { success: false };
      }
    } catch (err: any) {
      const errorMessage = err?.message || 'Network error occurred';
      setError(errorMessage);
      toast.error(errorMessage);
      return { success: false };
    } finally {
      setIsLoading(false);
    }
  }, [pendingAuth]);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Clear pending authentication state
   */
  const clearPendingAuth = useCallback(() => {
    setPendingAuth(null);
    setError(null);
  }, []);

  return {
    isLoading,
    error,
    pendingAuth,
    sendOtpWithRoomValidation,
    verifyOtpAndAuthenticate,
    clearError,
    clearPendingAuth,
  };
};

export default useEnhancedAuth;
