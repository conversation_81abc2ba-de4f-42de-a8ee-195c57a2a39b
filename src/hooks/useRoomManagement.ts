"use client";
import { useState, useCallback } from 'react';
import { toast } from 'sonner';
import { 
  getRoomMembers,
  createRoomWithOrder,
  isValidUUID
} from '@/lib/enhancedApi';
import { 
  RoomMember,
  GetRoomMembersResponse,
  CreateRoomWithOrderRequest,
  CreateRoomWithOrderResponse,
  ExtendedUserRole
} from '@/types/chat';

interface UseRoomManagementReturn {
  isLoading: boolean;
  error: string | null;
  roomMembers: RoomMember[];
  roomData: GetRoomMembersResponse['room'] | null;
  totalMembers: number;
  fetchRoomMembers: (roomId: string) => Promise<boolean>;
  createRoomWithOrderValidation: (
    orderGuid: string,
    role: ExtendedUserRole
  ) => Promise<{ success: boolean; roomData?: CreateRoomWithOrderResponse }>;
  clearError: () => void;
  clearRoomData: () => void;
}

/**
 * Room management hook for enhanced room operations
 * Handles room member retrieval and room creation with order validation
 */
export const useRoomManagement = (): UseRoomManagementReturn => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [roomMembers, setRoomMembers] = useState<RoomMember[]>([]);
  const [roomData, setRoomData] = useState<GetRoomMembersResponse['room'] | null>(null);
  const [totalMembers, setTotalMembers] = useState(0);

  /**
   * Fetch room members with authentication validation
   * User must be part of the room to access member information
   */
  const fetchRoomMembers = useCallback(async (roomId: string): Promise<boolean> => {
    if (!roomId.trim()) {
      setError('Room ID is required');
      toast.error('Room ID is required');
      return false;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await getRoomMembers(roomId.trim());

      if (response.statusCode === 200 && response.data) {
        const { room, members, total_members } = response.data;
        
        setRoomData(room);
        setRoomMembers(members);
        setTotalMembers(total_members);
        
        toast.success(`Loaded ${total_members} room members`);
        return true;
      } else {
        const errorMessage = response.message || 'Failed to fetch room members';
        setError(errorMessage);
        
        // Handle specific error cases
        if (response.statusCode === 401) {
          toast.error('Authentication required. Please log in again.');
        } else if (response.statusCode === 403) {
          toast.error('You are not authorized to view members of this room');
        } else if (response.statusCode === 404) {
          toast.error('Room not found');
        } else {
          toast.error(errorMessage);
        }
        
        return false;
      }
    } catch (err: any) {
      const errorMessage = err?.message || 'Network error occurred';
      setError(errorMessage);
      toast.error(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Create room with order validation
   * Validates provider/user order relationships before creating or joining room
   */
  const createRoomWithOrderValidation = useCallback(async (
    orderGuid: string,
    role: ExtendedUserRole
  ): Promise<{ success: boolean; roomData?: CreateRoomWithOrderResponse }> => {
    // Input validation
    if (!orderGuid.trim()) {
      setError('Order GUID is required');
      toast.error('Order GUID is required');
      return { success: false };
    }

    if (!isValidUUID(orderGuid.trim())) {
      setError('Invalid order GUID format');
      toast.error('Invalid order GUID format');
      return { success: false };
    }

    if (!role) {
      setError('User role is required');
      toast.error('User role is required');
      return { success: false };
    }

    setIsLoading(true);
    setError(null);

    try {
      const request: CreateRoomWithOrderRequest = {
        order_guid: orderGuid.trim(),
        role,
      };

      const response = await createRoomWithOrder(request);

      if (response.statusCode === 200 && response.data) {
        const roomData = response.data;
        
        // Show appropriate success message
        const message = roomData.is_new_room 
          ? 'New room created successfully!' 
          : 'Joined existing room successfully!';
        
        toast.success(message);
        return { success: true, roomData };
      } else {
        const errorMessage = response.message || 'Failed to create/join room';
        setError(errorMessage);
        
        // Handle specific error cases
        if (response.statusCode === 401) {
          toast.error('Authentication required. Please log in again.');
        } else if (response.statusCode === 403) {
          toast.error('You are not authorized to create a room for this order');
        } else if (response.statusCode === 404) {
          toast.error('Order not found');
        } else {
          toast.error(errorMessage);
        }
        
        return { success: false };
      }
    } catch (err: any) {
      const errorMessage = err?.message || 'Network error occurred';
      setError(errorMessage);
      toast.error(errorMessage);
      return { success: false };
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Clear all room data
   */
  const clearRoomData = useCallback(() => {
    setRoomMembers([]);
    setRoomData(null);
    setTotalMembers(0);
    setError(null);
  }, []);

  return {
    isLoading,
    error,
    roomMembers,
    roomData,
    totalMembers,
    fetchRoomMembers,
    createRoomWithOrderValidation,
    clearError,
    clearRoomData,
  };
};

export default useRoomManagement;
