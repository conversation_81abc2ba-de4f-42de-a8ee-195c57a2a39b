"use client";
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Users, RefreshCw, Eye, EyeOff } from 'lucide-react';
import { useRoomManagement } from '@/hooks/useRoomManagement';
import { RoomMember } from '@/types/chat';

interface RoomMembersWidgetProps {
  roomId: string;
  className?: string;
  autoLoad?: boolean;
  showRefreshButton?: boolean;
  collapsible?: boolean;
}

/**
 * Room Members Widget - A reusable component for displaying room members
 * Uses the new getRoomMembers API with token protection
 * Can be easily integrated into existing chat components
 */
export const RoomMembersWidget: React.FC<RoomMembersWidgetProps> = ({
  roomId,
  className = '',
  autoLoad = true,
  showRefreshButton = true,
  collapsible = false,
}) => {
  const {
    isLoading,
    error,
    roomMembers,
    roomData,
    totalMembers,
    fetchRoomMembers,
    clearError,
  } = useRoomManagement();

  const [isCollapsed, setIsCollapsed] = useState(false);

  // Auto-load room members on mount if enabled
  useEffect(() => {
    if (autoLoad && roomId) {
      fetchRoomMembers(roomId);
    }
  }, [autoLoad, roomId, fetchRoomMembers]);

  const handleRefresh = () => {
    if (roomId) {
      fetchRoomMembers(roomId);
    }
  };

  const getRoleColor = (role: string): string => {
    switch (role.toLowerCase()) {
      case 'buser':
      case 'provider':
        return 'bg-blue-100 text-blue-800';
      case 'user':
      case 'patient':
        return 'bg-green-100 text-green-800';
      case 'support_admin':
      case 'group_admin':
        return 'bg-purple-100 text-purple-800';
      case 'medical_assistant':
        return 'bg-orange-100 text-orange-800';
      case 'pharmacist':
        return 'bg-teal-100 text-teal-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string): string => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (!roomId) {
    return (
      <Alert>
        <AlertDescription>
          Room ID is required to display members.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            <CardTitle className="text-lg">
              Room Members
              {totalMembers > 0 && (
                <span className="ml-2 text-sm font-normal text-muted-foreground">
                  ({totalMembers})
                </span>
              )}
            </CardTitle>
          </div>
          <div className="flex items-center gap-2">
            {showRefreshButton && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={isLoading}
              >
                <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              </Button>
            )}
            {collapsible && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsCollapsed(!isCollapsed)}
              >
                {isCollapsed ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
              </Button>
            )}
          </div>
        </div>
        {roomData && (
          <CardDescription>
            {roomData.room_name} (ID: {roomData.room_identifier})
          </CardDescription>
        )}
      </CardHeader>

      {!isCollapsed && (
        <CardContent>
          {/* Error Display */}
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription className="flex items-center justify-between">
                {error}
                <Button variant="ghost" size="sm" onClick={clearError}>
                  Dismiss
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {/* Loading State */}
          {isLoading && (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-6 h-6 animate-spin mr-2" />
              <span className="text-muted-foreground">Loading room members...</span>
            </div>
          )}

          {/* No Members State */}
          {!isLoading && !error && roomMembers.length === 0 && (
            <div className="text-center py-8">
              <Users className="w-12 h-12 mx-auto text-muted-foreground mb-2" />
              <p className="text-muted-foreground">No members found</p>
              <Button variant="outline" size="sm" onClick={handleRefresh} className="mt-2">
                Try Again
              </Button>
            </div>
          )}

          {/* Members List */}
          {!isLoading && roomMembers.length > 0 && (
            <div className="space-y-3">
              {roomMembers.map((member: RoomMember) => (
                <div
                  key={member.user_id}
                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium text-sm">
                        {member.name || `${member.first_name} ${member.last_name}`}
                      </h4>
                      <Badge variant="secondary" className={`text-xs ${getRoleColor(member.role)}`}>
                        {member.role}
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground">{member.email}</p>
                  </div>
                  <div className="flex flex-col items-end gap-1">
                    <Badge variant="outline" className={`text-xs ${getStatusColor(member.status)}`}>
                      {member.status}
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      ID: {member.user_id}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Load Button for Manual Loading */}
          {!autoLoad && !isLoading && roomMembers.length === 0 && !error && (
            <div className="text-center py-4">
              <Button onClick={handleRefresh} variant="outline">
                <Users className="w-4 h-4 mr-2" />
                Load Room Members
              </Button>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
};

export default RoomMembersWidget;
