"use client";
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Plus } from "lucide-react";
import { useRoomManagement } from "@/hooks/useRoomManagement";
import { ExtendedUserRole } from "@/types/chat";
import { isValidUUID } from "@/lib/enhancedApi";

interface CreateRoomWidgetProps {
  className?: string;
  onRoomCreated?: (roomData: any) => void;
  defaultRole?: ExtendedUserRole;
  autoRedirect?: boolean;
}

/**
 * Create Room Widget - A reusable component for creating rooms with order validation
 * Uses the new createRoomWithOrder API with provider/user order relationship validation
 * Can be easily integrated into existing components
 */
export const CreateRoomWidget: React.FC<CreateRoomWidgetProps> = ({
  className = "",
  onRoomCreated,
  defaultRole = "USER",
  autoRedirect = false,
}) => {
  const router = useRouter();
  const { isLoading, error, createRoomWithOrderValidation, clearError } =
    useRoomManagement();

  const [orderGuid, setOrderGuid] = useState("");
  const [role, setRole] = useState<ExtendedUserRole>(defaultRole);
  const [validationError, setValidationError] = useState<string | null>(null);

  const validateInputs = (): boolean => {
    setValidationError(null);

    if (!orderGuid.trim()) {
      setValidationError("Order GUID is required");
      return false;
    }

    if (!isValidUUID(orderGuid.trim())) {
      setValidationError("Please enter a valid UUID format for Order GUID");
      return false;
    }

    if (!role) {
      setValidationError("Role selection is required");
      return false;
    }

    return true;
  };

  const handleCreateRoom = async () => {
    if (!validateInputs()) {
      return;
    }

    clearError();

    try {
      const result = await createRoomWithOrderValidation(
        orderGuid.trim(),
        role,
      );

      if (result.success && result.roomData) {
        // Call the callback if provided
        if (onRoomCreated) {
          onRoomCreated(result.roomData);
        }

        // Auto-redirect to the room if enabled
        if (autoRedirect && result.roomData.room_url) {
          router.push(result.roomData.room_url);
        }

        // Clear the form on success
        setOrderGuid("");
        setValidationError(null);
      }
    } catch (err) {
      console.error("Failed to create room:", err);
    }
  };

  const handleOrderGuidChange = (value: string) => {
    setOrderGuid(value);
    // Clear validation error when user starts typing
    if (validationError) {
      setValidationError(null);
    }
  };

  const getRoleDescription = (selectedRole: ExtendedUserRole): string => {
    switch (selectedRole) {
      case "USER":
        return "Patient - Must be the patient who answered the order";
      case "BUSER":
        return "Provider - Must be the assigned provider for the order";
      case "SUPPORT_ADMIN":
        return "Support Admin - Can access any order";
      case "GROUP_ADMIN":
        return "Group Admin - Can access any order";
      case "medical_assistant":
        return "Medical Assistant - Limited access based on assignment";
      case "pharmacist":
        return "Pharmacist - Access based on pharmacy assignment";
      default:
        return "Select a role to see description";
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Plus className="w-5 h-5" />
          Create/Join Room
        </CardTitle>
        <CardDescription>
          Create a new room or join an existing room using order validation
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Order GUID Input */}
        <div className="space-y-2">
          <Label htmlFor="orderGuid">Order GUID *</Label>
          <Input
            id="orderGuid"
            value={orderGuid}
            onChange={(e) => handleOrderGuidChange(e.target.value)}
            placeholder="Enter order GUID (UUID format)"
            className={validationError ? "border-red-500" : ""}
          />
          <p className="text-xs text-muted-foreground">
            Must be a valid UUID format (e.g.,
            550e8400-e29b-41d4-a716-************)
          </p>
        </div>

        {/* Role Selection */}
        <div className="space-y-2">
          <Label htmlFor="role">Role *</Label>
          <Select
            value={role}
            onValueChange={(value) => setRole(value as ExtendedUserRole)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select your role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="USER">User (Patient)</SelectItem>
              <SelectItem value="BUSER">Provider</SelectItem>
              <SelectItem value="SUPPORT_ADMIN">Support Admin</SelectItem>
              <SelectItem value="GROUP_ADMIN">Group Admin</SelectItem>
              <SelectItem value="medical_assistant">
                Medical Assistant
              </SelectItem>
              <SelectItem value="pharmacist">Pharmacist</SelectItem>
            </SelectContent>
          </Select>
          <p className="text-xs text-muted-foreground">
            {getRoleDescription(role)}
          </p>
        </div>

        {/* Validation Error */}
        {validationError && (
          <Alert variant="destructive">
            <AlertDescription>{validationError}</AlertDescription>
          </Alert>
        )}

        {/* API Error */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription className="flex items-center justify-between">
              {error}
              <Button variant="ghost" size="sm" onClick={clearError}>
                Dismiss
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Create Room Button */}
        <Button
          onClick={handleCreateRoom}
          disabled={isLoading || !orderGuid.trim() || !role}
          className="w-full"
        >
          {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
          {isLoading ? "Creating/Joining Room..." : "Create/Join Room"}
        </Button>

        {/* Info Box */}
        <div className="p-3 bg-muted rounded-lg">
          <h4 className="text-sm font-medium mb-2">How it works:</h4>
          <ul className="text-xs text-muted-foreground space-y-1">
            <li>
              • If a room exists for this order, you'll be added as a member
            </li>
            <li>• If no room exists, a new room will be created</li>
            <li>
              • Your access is validated based on your role and order
              relationship
            </li>
            <li>• Providers must be assigned to the order</li>
            <li>• Patients must be the ones who answered the order</li>
            <li>• Admins can access any order</li>
          </ul>
        </div>

        {/* Sample UUID Generator */}
        <div className="pt-2 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // Generate a sample UUID for testing
              const sampleUuid = "550e8400-e29b-41d4-a716-************";
              setOrderGuid(sampleUuid);
            }}
            className="text-xs"
          >
            Use Sample UUID (for testing)
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default CreateRoomWidget;
