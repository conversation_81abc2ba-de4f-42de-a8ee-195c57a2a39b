"use client";
import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Users, Plus, Mail, Shield } from 'lucide-react';
import { useEnhancedAuth } from '@/hooks/useEnhancedAuth';
import { useRoomManagement } from '@/hooks/useRoomManagement';
import { ExtendedUserRole } from '@/types/chat';

/**
 * Demo component showcasing all four new enhanced APIs
 * This component demonstrates the complete flow of the new authentication and room management features
 */
export const EnhancedAuthDemo: React.FC = () => {
  // Enhanced authentication hook
  const {
    isLoading: authLoading,
    error: authError,
    pendingAuth,
    sendOtpWithRoomValidation,
    verifyOtpAndAuthenticate,
    clearError: clearAuthError,
    clearPendingAuth,
  } = useEnhancedAuth();

  // Room management hook
  const {
    isLoading: roomLoading,
    error: roomError,
    roomMembers,
    roomData,
    totalMembers,
    fetchRoomMembers,
    createRoomWithOrderValidation,
    clearError: clearRoomError,
    clearRoomData,
  } = useRoomManagement();

  // Form states
  const [roomIdentifier, setRoomIdentifier] = useState('');
  const [email, setEmail] = useState('');
  const [role, setRole] = useState<ExtendedUserRole>('USER');
  const [otp, setOtp] = useState('');
  const [orderGuid, setOrderGuid] = useState('');
  const [roomIdForMembers, setRoomIdForMembers] = useState('');

  // UI state
  const [activeTab, setActiveTab] = useState<'auth' | 'room'>('auth');

  const handleSendOtp = async () => {
    const success = await sendOtpWithRoomValidation(roomIdentifier, email, role);
    if (success) {
      console.log('OTP sent successfully');
    }
  };

  const handleVerifyOtp = async () => {
    const result = await verifyOtpAndAuthenticate(otp);
    if (result.success) {
      console.log('Authentication successful:', result.user);
      setOtp('');
    }
  };

  const handleFetchMembers = async () => {
    const success = await fetchRoomMembers(roomIdForMembers);
    if (success) {
      console.log('Room members fetched successfully');
    }
  };

  const handleCreateRoom = async () => {
    const result = await createRoomWithOrderValidation(orderGuid, role);
    if (result.success) {
      console.log('Room created/joined successfully:', result.roomData);
    }
  };

  const isLoading = authLoading || roomLoading;

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold mb-2">Enhanced Chat APIs Demo</h1>
        <p className="text-muted-foreground">
          Demonstration of the four new enhanced APIs with room validation and security features
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-4 mb-6">
        <Button
          variant={activeTab === 'auth' ? 'default' : 'outline'}
          onClick={() => setActiveTab('auth')}
          className="flex items-center gap-2"
        >
          <Shield className="w-4 h-4" />
          Authentication
        </Button>
        <Button
          variant={activeTab === 'room' ? 'default' : 'outline'}
          onClick={() => setActiveTab('room')}
          className="flex items-center gap-2"
        >
          <Users className="w-4 h-4" />
          Room Management
        </Button>
      </div>

      {/* Authentication Tab */}
      {activeTab === 'auth' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* API 1: Send OTP with Room Validation */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="w-5 h-5" />
                Send OTP with Room Validation
              </CardTitle>
              <CardDescription>
                Send OTP with room membership validation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="roomIdentifier">Room Identifier</Label>
                <Input
                  id="roomIdentifier"
                  value={roomIdentifier}
                  onChange={(e) => setRoomIdentifier(e.target.value)}
                  placeholder="Enter room identifier"
                />
              </div>
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter email address"
                />
              </div>
              <div>
                <Label htmlFor="role">Role</Label>
                <Select value={role} onValueChange={(value) => setRole(value as ExtendedUserRole)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USER">User</SelectItem>
                    <SelectItem value="BUSER">Provider</SelectItem>
                    <SelectItem value="SUPPORT_ADMIN">Support Admin</SelectItem>
                    <SelectItem value="GROUP_ADMIN">Group Admin</SelectItem>
                    <SelectItem value="medical_assistant">Medical Assistant</SelectItem>
                    <SelectItem value="pharmacist">Pharmacist</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button 
                onClick={handleSendOtp} 
                disabled={isLoading || !roomIdentifier || !email}
                className="w-full"
              >
                {authLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                Send OTP
              </Button>
            </CardContent>
          </Card>

          {/* API 2: Enhanced OTP Verification */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="w-5 h-5" />
                Enhanced OTP Verification
              </CardTitle>
              <CardDescription>
                Verify OTP and complete authentication
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="otp">OTP Code</Label>
                <Input
                  id="otp"
                  value={otp}
                  onChange={(e) => setOtp(e.target.value)}
                  placeholder="Enter OTP code"
                  maxLength={6}
                />
              </div>
              {pendingAuth && (
                <div className="text-sm text-muted-foreground">
                  <p>Pending authentication for:</p>
                  <p className="font-medium">{pendingAuth.email}</p>
                  <p className="font-medium">Room: {pendingAuth.room_identifier}</p>
                </div>
              )}
              <Button 
                onClick={handleVerifyOtp} 
                disabled={isLoading || !pendingAuth || !otp}
                className="w-full"
              >
                {authLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                Verify OTP
              </Button>
              {pendingAuth && (
                <Button 
                  variant="outline" 
                  onClick={clearPendingAuth}
                  className="w-full"
                >
                  Cancel Authentication
                </Button>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Room Management Tab */}
      {activeTab === 'room' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* API 3: Get Room Members */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                Get Room Members
              </CardTitle>
              <CardDescription>
                Fetch room members (token protected)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="roomIdForMembers">Room ID</Label>
                <Input
                  id="roomIdForMembers"
                  value={roomIdForMembers}
                  onChange={(e) => setRoomIdForMembers(e.target.value)}
                  placeholder="Enter room ID"
                />
              </div>
              <Button 
                onClick={handleFetchMembers} 
                disabled={isLoading || !roomIdForMembers}
                className="w-full"
              >
                {roomLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                Fetch Members
              </Button>
              
              {roomData && (
                <div className="mt-4 p-3 bg-muted rounded-lg">
                  <h4 className="font-medium">{roomData.room_name}</h4>
                  <p className="text-sm text-muted-foreground">
                    Total Members: {totalMembers}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* API 4: Create Room with Order */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Plus className="w-5 h-5" />
                Create Room with Order
              </CardTitle>
              <CardDescription>
                Create/join room with order validation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="orderGuid">Order GUID</Label>
                <Input
                  id="orderGuid"
                  value={orderGuid}
                  onChange={(e) => setOrderGuid(e.target.value)}
                  placeholder="Enter order GUID (UUID format)"
                />
              </div>
              <div>
                <Label htmlFor="createRole">Role</Label>
                <Select value={role} onValueChange={(value) => setRole(value as ExtendedUserRole)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USER">User</SelectItem>
                    <SelectItem value="BUSER">Provider</SelectItem>
                    <SelectItem value="SUPPORT_ADMIN">Support Admin</SelectItem>
                    <SelectItem value="GROUP_ADMIN">Group Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button 
                onClick={handleCreateRoom} 
                disabled={isLoading || !orderGuid}
                className="w-full"
              >
                {roomLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                Create/Join Room
              </Button>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Error Display */}
      {(authError || roomError) && (
        <Alert variant="destructive">
          <AlertDescription>
            {authError || roomError}
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => {
                clearAuthError();
                clearRoomError();
              }}
              className="ml-2"
            >
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Room Members Display */}
      {roomMembers.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Room Members</CardTitle>
            <CardDescription>
              Members of {roomData?.room_name} ({totalMembers} total)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {roomMembers.map((member) => (
                <div key={member.user_id} className="flex justify-between items-center p-2 border rounded">
                  <div>
                    <p className="font-medium">{member.name}</p>
                    <p className="text-sm text-muted-foreground">{member.email}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">{member.role}</p>
                    <p className="text-xs text-muted-foreground">{member.status}</p>
                  </div>
                </div>
              ))}
            </div>
            <Button 
              variant="outline" 
              onClick={clearRoomData}
              className="mt-4 w-full"
            >
              Clear Data
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default EnhancedAuthDemo;
