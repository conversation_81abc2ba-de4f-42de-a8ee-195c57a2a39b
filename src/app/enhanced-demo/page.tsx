"use client";
import React from 'react';
import { EnhancedAuthDemo } from '@/components/enhanced-auth/EnhancedAuthDemo';

/**
 * Enhanced APIs Demo Page
 * 
 * This page demonstrates all four new enhanced APIs:
 * 1. Send OTP with Room Validation
 * 2. Enhanced OTP Verification  
 * 3. Get Room Members (Token Protected)
 * 4. Create Room with Order Validation
 * 
 * Access this page at: /enhanced-demo
 */
export default function EnhancedDemoPage() {
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto py-8">
        <div className="mb-8 text-center">
          <h1 className="text-4xl font-bold mb-4">Enhanced Chat APIs Demo</h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            This demo showcases the four new enhanced APIs for the provider portal chat module.
            Test the room-based authentication, member management, and order-validated room creation features.
          </p>
        </div>
        
        <EnhancedAuthDemo />
        
        <div className="mt-12 p-6 bg-muted rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Integration Guide</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
            <div>
              <h3 className="font-medium mb-2">Quick Integration</h3>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Import hooks: <code>useEnhancedAuth</code>, <code>useRoomManagement</code></li>
                <li>• Use widgets: <code>RoomMembersWidget</code>, <code>CreateRoomWidget</code></li>
                <li>• Enhanced auth methods available in <code>useAuth()</code> context</li>
                <li>• All APIs work alongside existing authentication</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium mb-2">Key Features</h3>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Room membership validation before OTP sending</li>
                <li>• Enhanced token handling with improved security</li>
                <li>• Token-protected room member retrieval</li>
                <li>• Order-relationship validation for room access</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
