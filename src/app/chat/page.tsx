"use client";
import React, { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from "@/context/auth";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { MessageCircle } from "lucide-react";
import { withAuth } from "@/context/auth";
import { Loader } from "@/components/shared/loader";

/**
 * Chat Home Page - Room Selection
 * Redirects to room-specific routes
 */
const ChatPage: React.FC = () => {
  const { user, logout } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get room ID from URL query parameter
  const roomId = searchParams.get("room");

  const [roomIdInput, setRoomIdInput] = useState("");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Redirect old URL format to new format
    if (roomId) {
      router.replace(`/chat/${roomId}`);
      return;
    }
    setIsLoading(false);
  }, [roomId, router]);

  const handleRoomSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (roomIdInput.trim()) {
      router.push(`/chat/${roomIdInput.trim()}`);
    }
  };

  const getRoleDisplayName = () => {
    switch (user?.role) {
      case "USER":
        return "Patient";
      case "PROVIDER":
        return "Healthcare Provider";
      case "ADMIN":
        return "Administrator";
      default:
        return "User";
    }
  };

  if (isLoading) {
    return <Loader show={true} />;
  }

  // Show room selection interface
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto h-12 w-12 text-blue-600 mb-4">
            <MessageCircle className="h-12 w-12" />
          </div>
          <CardTitle>Welcome, {user?.first_name}!</CardTitle>
          <CardDescription>
            You are logged in as: {getRoleDisplayName()}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleRoomSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="roomId">Enter Room ID</Label>
              <Input
                id="roomId"
                type="text"
                value={roomIdInput}
                onChange={(e) => setRoomIdInput(e.target.value)}
                placeholder="Enter room ID to join chat"
                required
              />
            </div>
            <Button type="submit" className="w-full">
              Join Chat Room
            </Button>
          </form>

          <div className="mt-6 pt-6 border-t">
            <Button variant="outline" onClick={logout} className="w-full">
              Logout
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default withAuth(ChatPage);
