"use client";
import React, { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from "@/context/auth";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { MessageCircle, Settings } from "lucide-react";
import { Loader } from "@/components/shared/loader";
import { toast } from "sonner";
import { apiClient } from "@/lib/api";
import { ApiResponse, ChatSettings, UserRole } from "@/types/chat";
import UnifiedChatComponent from "@/components/chat/unified-chat";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { LoginForm } from "@/components/auth/login-form";

interface ChatRoomPageProps {
  params: {
    roomId: string;
  };
}

/**
 * Dynamic Chat Room Page
 * Handles /chat/[roomId] routes with inline authentication
 * Supports role-based access with USER as default
 */
const ChatRoomPage: React.FC<ChatRoomPageProps> = ({ params }) => {
  const { user, isAuthenticated, logout, token } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Extract room ID from URL params
  const { roomId } = params;
  
  // Extract URL parameters for role and email
  const urlRole = searchParams.get("role") as UserRole;
  const urlEmail = searchParams.get("email");
  
  // Determine role (default to USER if not specified)
  const defaultRole: UserRole = urlRole || "USER";
  
  const [chatData, setChatData] = useState<any>(null);
  const [chatSettings, setChatSettings] = useState<ChatSettings>({
    isEnabled: true,
    canToggle: false,
  });
  const [isLoading, setIsLoading] = useState(true);

  // Determine if user can toggle chat (PROVIDER and ADMIN only)
  const canToggleChat = user?.role === "PROVIDER" || user?.role === "ADMIN";

  // Determine if chat is disabled for USER
  const isChatDisabledForUser =
    user?.role === "USER" && !chatSettings.isEnabled;

  useEffect(() => {
    if (isAuthenticated && roomId) {
      loadChatData(roomId);
    } else if (isAuthenticated) {
      setIsLoading(false);
    } else {
      setIsLoading(false);
    }
  }, [roomId, isAuthenticated]);

  const loadChatData = async (roomIdentifier: string) => {
    if (!token) return;

    setIsLoading(true);
    try {
      // Load chat metadata
      const response: ApiResponse = await apiClient({
        method: "GET",
        endpoint: `chat/${roomIdentifier}/metadata`,
        token,
      });

      if (response.statusCode === 200) {
        setChatData(response.data);

        // Load chat settings for PROVIDER/ADMIN
        if (canToggleChat) {
          await loadChatSettings(roomIdentifier);
        }
      } else {
        toast.error(response.message || "Failed to load chat data");
      }
    } catch (error) {
      console.error("Failed to load chat data:", error);
      toast.error("Failed to load chat data");
    } finally {
      setIsLoading(false);
    }
  };

  const loadChatSettings = async (roomIdentifier: string) => {
    if (!token) return;

    try {
      const response: ApiResponse = await apiClient({
        method: "GET",
        endpoint: `room/${roomIdentifier}/settings`,
        token,
      });

      if (response.statusCode === 200) {
        setChatSettings({
          isEnabled: response.data.isEnabled,
          canToggle: canToggleChat,
        });
      }
    } catch (error) {
      console.error("Failed to load chat settings:", error);
      // Default to enabled if we can't load settings
      setChatSettings({ isEnabled: true, canToggle: canToggleChat });
    }
  };

  const toggleChatStatus = async () => {
    if (!roomId || !token || !canToggleChat) return;

    try {
      const newStatus = !chatSettings.isEnabled;
      const response: ApiResponse = await apiClient({
        method: "POST",
        endpoint: `room/${roomId}/${newStatus ? "enable" : "disable"}`,
        data: {},
        token,
      });

      if (response.statusCode === 200) {
        setChatSettings((prev) => ({ ...prev, isEnabled: newStatus }));
        toast.success(
          `Chat ${newStatus ? "enabled" : "disabled"} successfully`,
        );
      } else {
        toast.error(response.message || "Failed to toggle chat status");
      }
    } catch (error) {
      console.error("Failed to toggle chat status:", error);
      toast.error("Failed to toggle chat status");
    }
  };

  // Show loading state
  if (isLoading) {
    return <Loader show={true} />;
  }

  // Show inline login if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <div className="text-center mb-6">
            <div className="mx-auto h-12 w-12 text-blue-600 mb-4">
              <MessageCircle className="h-12 w-12" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900">Join Chat Room</h1>
            <p className="text-sm text-gray-600 mt-2">
              Room ID: {roomId}
            </p>
          </div>
          
          <LoginForm
            defaultRole={defaultRole}
            defaultEmail={urlEmail || ""}
            title="Sign In to Chat"
            description="Enter your email to receive an OTP and join the chat"
            hideRoleSelector={true}
          />
        </div>
      </div>
    );
  }

  // Show disabled message for USER when chat is disabled
  if (isChatDisabledForUser) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md text-center">
          <CardHeader>
            <div className="mx-auto h-12 w-12 text-gray-400 mb-4">
              <MessageCircle className="h-12 w-12" />
            </div>
            <CardTitle>Chat Unavailable</CardTitle>
            <CardDescription>
              The chat is currently disabled. Please wait for your healthcare
              provider to enable it.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              variant="outline"
              onClick={() => router.push("/chat")}
              className="w-full"
            >
              Back to Room Selection
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show room not found if no chat data
  if (!chatData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md text-center">
          <CardHeader>
            <CardTitle>Room Not Found</CardTitle>
            <CardDescription>
              The specified chat room could not be found.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => router.push("/chat")} className="w-full">
              Back to Room Selection
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show chat interface
  return (
    <div className="h-screen flex flex-col">
      {/* Chat Controls for PROVIDER/ADMIN */}
      {canToggleChat && (
        <div className="bg-white border-b px-4 py-2 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Settings className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium">Chat Controls</span>
          </div>
          <div className="flex items-center space-x-2">
            <Label htmlFor="chat-toggle" className="text-sm">
              {chatSettings.isEnabled ? "Enabled" : "Disabled"}
            </Label>
            <Switch
              id="chat-toggle"
              checked={chatSettings.isEnabled}
              onCheckedChange={toggleChatStatus}
            />
          </div>
        </div>
      )}

      {/* Chat Interface */}
      <div className="flex-1">
        <UnifiedChatComponent
          patient={chatData.patient}
          provider={chatData.provider}
          room={chatData.room}
          role={user?.role || "USER"}
        />
      </div>
    </div>
  );
};

export default ChatRoomPage;
