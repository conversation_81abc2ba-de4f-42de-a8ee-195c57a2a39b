"use client";
import { apiClient } from '@/lib/api';
import { tokenManager } from '@/lib/tokenManager';
import { 
  SendOtpWithValidationRequest,
  SendOtpWithValidationResponse,
  VerifyOtpEnhancedRequest,
  VerifyOtpEnhancedResponse,
  GetRoomMembersResponse,
  CreateRoomWithOrderRequest,
  CreateRoomWithOrderResponse,
  ApiResponse,
  API_ENDPOINTS
} from '@/types/chat';

/**
 * Enhanced API service for the new provider portal chat module APIs
 * Implements the four new APIs with enhanced validation and security features
 */

/**
 * 1. Enhanced OTP Sending with Room Validation
 * Sends OTP to user with room validation ensuring the user is part of the specified room
 */
export const sendOtpWithValidation = async (
  request: SendOtpWithValidationRequest
): Promise<ApiResponse<SendOtpWithValidationResponse>> => {
  try {
    const response = await apiClient({
      method: 'POST',
      endpoint: API_ENDPOINTS.AUTH.SEND_OTP_WITH_VALIDATION,
      data: request,
    });

    return response;
  } catch (error: any) {
    console.error('Failed to send OTP with validation:', error);
    return {
      statusCode: 500,
      message: error?.message || 'Failed to send OTP with validation',
      error: error?.message,
    };
  }
};

/**
 * 2. Enhanced OTP Verification
 * Verifies OTP using email and returns enhanced user token with improved validation
 */
export const verifyOtpEnhanced = async (
  request: VerifyOtpEnhancedRequest
): Promise<ApiResponse<VerifyOtpEnhancedResponse>> => {
  try {
    const response = await apiClient({
      method: 'POST',
      endpoint: API_ENDPOINTS.AUTH.VERIFY_OTP_ENHANCED,
      data: request,
    });

    return response;
  } catch (error: any) {
    console.error('Failed to verify OTP:', error);
    return {
      statusCode: 500,
      message: error?.message || 'Failed to verify OTP',
      error: error?.message,
    };
  }
};

/**
 * 3. Get Room Members (Token Protected)
 * Retrieves room member names and roles. User must be part of the room.
 */
export const getRoomMembers = async (
  roomId: string
): Promise<ApiResponse<GetRoomMembersResponse>> => {
  try {
    const token = await tokenManager.getToken();
    if (!token) {
      return {
        statusCode: 401,
        message: 'Authentication required',
        error: 'No valid token found',
      };
    }

    const endpoint = API_ENDPOINTS.ROOM.MEMBERS.replace('{roomId}', roomId);
    const response = await apiClient({
      method: 'GET',
      endpoint,
      token,
    });

    return response;
  } catch (error: any) {
    console.error('Failed to get room members:', error);
    return {
      statusCode: 500,
      message: error?.message || 'Failed to get room members',
      error: error?.message,
    };
  }
};

/**
 * 4. Advanced Room Creation with Order Validation
 * Creates room with orderGuid and role validation. Validates provider/user order relationships.
 */
export const createRoomWithOrder = async (
  request: CreateRoomWithOrderRequest
): Promise<ApiResponse<CreateRoomWithOrderResponse>> => {
  try {
    const token = await tokenManager.getToken();
    if (!token) {
      return {
        statusCode: 401,
        message: 'Authentication required',
        error: 'No valid token found',
      };
    }

    const response = await apiClient({
      method: 'POST',
      endpoint: API_ENDPOINTS.ROOM.CREATE_WITH_ORDER,
      data: request,
      token,
    });

    return response;
  } catch (error: any) {
    console.error('Failed to create room with order:', error);
    return {
      statusCode: 500,
      message: error?.message || 'Failed to create room with order',
      error: error?.message,
    };
  }
};

/**
 * Utility function to validate UUID format
 */
export const isValidUUID = (uuid: string): boolean => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
};

/**
 * Utility function to validate email format
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Enhanced API client wrapper with better error handling and validation
 */
export const enhancedApiClient = {
  sendOtpWithValidation,
  verifyOtpEnhanced,
  getRoomMembers,
  createRoomWithOrder,
  isValidUUID,
  isValidEmail,
};

export default enhancedApiClient;
