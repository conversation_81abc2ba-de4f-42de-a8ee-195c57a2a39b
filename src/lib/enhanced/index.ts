/**
 * Enhanced Chat APIs - Main Export File
 *
 * This file provides a centralized export for all enhanced API functionality
 * including hooks, components, and utility functions.
 */

// Core API functions
export {
  sendOtpWithValidation,
  verifyOtpEnhanced,
  getRoomMembers,
  createRoomWithOrder,
  isValidUUID,
  isValidEmail,
  enhancedApiClient,
} from "../enhancedApi";

// Hooks
export { useEnhancedAuth } from "../../hooks/useEnhancedAuth";
export { useRoomManagement } from "../../hooks/useRoomManagement";

// Components
export { EnhancedAuthDemo } from "../../components/enhanced-auth/EnhancedAuthDemo";
export { RoomMembersWidget } from "../../components/enhanced-auth/RoomMembersWidget";
export { CreateRoomWidget } from "../../components/enhanced-auth/CreateRoomWidget";

// Types
export type {
  ExtendedUserRole,
  SendOtpWithValidationRequest,
  SendOtpWithValidationResponse,
  VerifyOtpEnhancedRequest,
  VerifyOtpEnhancedResponse,
  RoomMember,
  GetRoomMembersResponse,
  CreateRoomWithOrderRequest,
  CreateRoomWithOrderResponse,
} from "../../types/chat";

// Re-export existing types that are commonly used with enhanced APIs
export type {
  User,
  UserRole,
  ApiResponse,
  AuthContextType,
} from "../../types/chat";

// Import the functions to include in the convenience object
import {
  sendOtpWithValidation,
  verifyOtpEnhanced,
  getRoomMembers,
  createRoomWithOrder,
  isValidUUID,
  isValidEmail,
} from "../enhancedApi";

/**
 * Convenience object for accessing all enhanced API functionality
 */
export const EnhancedChatAPI = {
  // API functions
  sendOtpWithValidation,
  verifyOtpEnhanced,
  getRoomMembers,
  createRoomWithOrder,

  // Utility functions
  isValidUUID,
  isValidEmail,

  // Hooks (these need to be imported separately due to React rules)
  hooks: {
    useEnhancedAuth: "useEnhancedAuth",
    useRoomManagement: "useRoomManagement",
  },

  // Components (these need to be imported separately due to React rules)
  components: {
    EnhancedAuthDemo: "EnhancedAuthDemo",
    RoomMembersWidget: "RoomMembersWidget",
    CreateRoomWidget: "CreateRoomWidget",
  },
} as const;
